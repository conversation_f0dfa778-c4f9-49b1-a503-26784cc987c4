"use client"

import { useState, useEffect, useMemo } from "react"
import { useLocalization } from "@/localization/functions/client"
import { knowledgeBaseLocales } from "./locales"

import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Loader2, Save, FileText, BarChart3 } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// BlockNote imports
import { BlockNoteEditor, PartialBlock } from "@blocknote/core"
import { useCreateBlockNote, BlockNoteViewRaw } from "@blocknote/react"
import "@blocknote/core/fonts/inter.css"
import "@blocknote/react/style.css"
import "./blocknote-custom.css"

import {
  KnowledgeBaseAPI,
  KnowledgeBaseEntry,
} from "@/lib/services/knowledgeBaseApi"

export default function KnowledgeBaseInputPage() {
  const { t } = useLocalization("knowledgeBase", knowledgeBaseLocales)

  const [content, setContent] = useState("")
  const [message, setMessage] = useState("")
  const [isStale, setIsStale] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // Track if we have an existing knowledge base (one per account)
  const [existingEntry, setExistingEntry] = useState<KnowledgeBaseEntry | null>(
    null,
  )
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const [kbStrength, setKbStrength] = useState<number>(0)
  const [totalEntries, setTotalEntries] = useState<number>(0)
  const [keywords, setKeywords] = useState<string[]>([])
  const [rules, setRules] = useState<string[]>([])
  const [templates, setTemplates] = useState<string[]>([])

  const fetchAnalytics = async () => {
    try {
      const res = await KnowledgeBaseAPI.GetAllStats().request()
      setKbStrength(res.score || 0)
      setTotalEntries(res.totalEntries || 0)
      setKeywords(res.keywords || [])
      setRules(res.inferredRules || [])
      setTemplates(res.suggestedTemplates || [])
    } catch (error) {
      console.error("Failed to fetch KB stats", error)
    }
  }

  const loadExistingKnowledgeBase = async () => {
    try {
      setLoading(true)
      // Get the user's single knowledge base (first entry for this organization)
      const res = await KnowledgeBaseAPI.GetAllEntries({
        limit: 1,
        page: 1,
      }).request()

      if (res.items && res.items.length > 0) {
        const entry = res.items[0]
        setExistingEntry(entry)
        setContent(entry.content)
        setHasUnsavedChanges(false)
      } else {
        // No existing knowledge base found, start fresh
        setExistingEntry(null)
        setContent("")
        setHasUnsavedChanges(false)
      }
    } catch (error) {
      console.error("Failed to load existing knowledge base", error)
      // If loading fails, start fresh
      setExistingEntry(null)
      setContent("")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadExistingKnowledgeBase()
    fetchAnalytics()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!content.trim()) {
      setMessage(t("knowledgeBase.content_empty_error"))
      return
    }

    try {
      setSaving(true)
      setMessage("")

      let result: KnowledgeBaseEntry

      console.log("Existing entry", existingEntry)

      if (existingEntry) {
        // Update the existing knowledge base
        result = await KnowledgeBaseAPI.UpdateEntry(existingEntry.id, {
          title: "Knowledge Base",
          content,
          category: "general",
          tags: ["knowledge-base"],
        }).request()
        setMessage(t("knowledgeBase.update_success"))
      } else {
        // Create the user's first knowledge base
        result = await KnowledgeBaseAPI.CreateEntry({
          title: "Knowledge Base",
          content,
          category: "general",
          tags: ["knowledge-base"],
        }).request()
        setMessage(t("knowledgeBase.save_success"))
        setExistingEntry(result) // Now we have the knowledge base for future updates
      }

      setHasUnsavedChanges(false)
      setIsStale(false)
      await fetchAnalytics()
    } catch (err: any) {
      console.error("Error saving knowledge base:", err)
      setMessage(err.message || t("knowledgeBase.save_error"))
    } finally {
      setSaving(false)
    }
  }

  // Initialize BlockNote editor
  const initialContent = useMemo(() => {
    if (!content) return undefined
    try {
      // Try to parse as JSON blocks first
      const parsed = JSON.parse(content)
      return Array.isArray(parsed) ? parsed as PartialBlock[] : undefined
    } catch {
      // If not JSON, convert plain text to blocks
      if (content.trim()) {
        return content.split('\n').map(line => ({
          type: "paragraph" as const,
          content: line || ""
        }))
      }
      return undefined
    }
  }, [])

  const editor = useCreateBlockNote({
    initialContent,
  })

  const handleEditorChange = () => {
    // Convert blocks to JSON string for storage
    const blocks = editor.document
    const jsonContent = JSON.stringify(blocks, null, 2)
    setContent(jsonContent)
    setHasUnsavedChanges(true)
    setIsStale(true)
  }

  // Get character count from blocks
  const getCharacterCount = () => {
    try {
      const blocks = editor.document
      return JSON.stringify(blocks).length
    } catch {
      return content.length
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>{t("knowledgeBase.loading")}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Editor - Takes up 2/3 of the space */}
          <div className="lg:col-span-2">
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                    <FileText className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {t("knowledgeBase.title")}
                    </CardTitle>
                    <p className="text-gray-600 mt-1">{t("knowledgeBase.description")}</p>
                  </div>
                </div>

                {existingEntry && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-700">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      {t("knowledgeBase.editing_info", {
                        date: new Date(existingEntry.updatedAt).toLocaleDateString()
                      })}
                    </div>
                  </div>
                )}

                {hasUnsavedChanges && (
                  <div className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg text-sm text-amber-700">
                    <div className="flex items-center gap-2">
                      <Save className="h-4 w-4" />
                      {t("knowledgeBase.unsaved_changes")}
                    </div>
                  </div>
                )}
              </CardHeader>

              <CardContent className="space-y-4">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-4">
                    <Label className="text-lg font-semibold flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      {t("knowledgeBase.label")}
                    </Label>

                    {/* Rich Text Editor */}
                    <div className="relative border-2 border-gray-200 rounded-lg overflow-hidden bg-white/50 backdrop-blur-sm focus-within:border-blue-500 transition-colors duration-200">
                      <div className="min-h-[600px]">
                        <BlockNoteViewRaw
                          editor={editor}
                          onChange={handleEditorChange}
                          theme="light"
                          className="blocknote-editor"
                        />
                      </div>
                      <div className="absolute bottom-4 right-4 text-xs text-gray-400 bg-white/80 px-2 py-1 rounded">
                        {getCharacterCount()} characters
                      </div>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-12 text-lg font-semibold bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg"
                    disabled={saving || (!content.trim() && editor.document.length === 0)}
                  >
                    {saving ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        {existingEntry ? t("knowledgeBase.updating") : t("knowledgeBase.saving")}
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-5 w-5" />
                        {existingEntry ? t("knowledgeBase.update_button") : t("knowledgeBase.save_button")}
                      </>
                    )}
                  </Button>

                  {message && (
                    <p
                      className={`text-sm pt-2 ${message.includes("success") || message.includes("updated")
                        ? "text-green-600"
                        : "text-red-600"
                        }`}
                    >
                      {message}
                    </p>
                  )}
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
