"use client"

import { useState, useEffect, useRef } from "react"
import { useLocalization } from "@/localization/functions/client"
import { knowledgeBaseLocales } from "./locales"

import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Loader2, Save, FileText, BarChart3, Type, Bold, Italic, List, Quote } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

import {
  KnowledgeBaseAPI,
  KnowledgeBaseEntry,
} from "@/lib/services/knowledgeBaseApi"

export default function KnowledgeBaseInputPage() {
  const { t } = useLocalization("knowledgeBase", knowledgeBaseLocales)

  const [content, setContent] = useState("")
  const [message, setMessage] = useState("")
  const [isStale, setIsStale] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // Track if we have an existing knowledge base (one per account)
  const [existingEntry, setExistingEntry] = useState<KnowledgeBaseEntry | null>(
    null,
  )
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const [kbStrength, setKbStrength] = useState<number>(0)
  const [totalEntries, setTotalEntries] = useState<number>(0)
  const [keywords, setKeywords] = useState<string[]>([])
  const [rules, setRules] = useState<string[]>([])
  const [templates, setTemplates] = useState<string[]>([])

  const fetchAnalytics = async () => {
    try {
      const res = await KnowledgeBaseAPI.GetAllStats().request()
      setKbStrength(res.score || 0)
      setTotalEntries(res.totalEntries || 0)
      setKeywords(res.keywords || [])
      setRules(res.inferredRules || [])
      setTemplates(res.suggestedTemplates || [])
    } catch (error) {
      console.error("Failed to fetch KB stats", error)
    }
  }

  const loadExistingKnowledgeBase = async () => {
    try {
      setLoading(true)
      // Get the user's single knowledge base (first entry for this organization)
      const res = await KnowledgeBaseAPI.GetAllEntries({
        limit: 1,
        page: 1,
      }).request()

      if (res.items && res.items.length > 0) {
        const entry = res.items[0]
        setExistingEntry(entry)
        setContent(entry.content)
        setHasUnsavedChanges(false)
      } else {
        // No existing knowledge base found, start fresh
        setExistingEntry(null)
        setContent("")
        setHasUnsavedChanges(false)
      }
    } catch (error) {
      console.error("Failed to load existing knowledge base", error)
      // If loading fails, start fresh
      setExistingEntry(null)
      setContent("")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadExistingKnowledgeBase()
    fetchAnalytics()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!content.trim()) {
      setMessage(t("knowledgeBase.content_empty_error"))
      return
    }

    try {
      setSaving(true)
      setMessage("")

      let result: KnowledgeBaseEntry

      console.log("Existing entry", existingEntry)

      if (existingEntry) {
        // Update the existing knowledge base
        result = await KnowledgeBaseAPI.UpdateEntry(existingEntry.id, {
          title: "Knowledge Base",
          content,
          category: "general",
          tags: ["knowledge-base"],
        }).request()
        setMessage(t("knowledgeBase.update_success"))
      } else {
        // Create the user's first knowledge base
        result = await KnowledgeBaseAPI.CreateEntry({
          title: "Knowledge Base",
          content,
          category: "general",
          tags: ["knowledge-base"],
        }).request()
        setMessage(t("knowledgeBase.save_success"))
        setExistingEntry(result) // Now we have the knowledge base for future updates
      }

      setHasUnsavedChanges(false)
      setIsStale(false)
      await fetchAnalytics()
    } catch (err: any) {
      console.error("Error saving knowledge base:", err)
      setMessage(err.message || t("knowledgeBase.save_error"))
    } finally {
      setSaving(false)
    }
  }

  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const handleContentChange = (value: string) => {
    setContent(value)
    setHasUnsavedChanges(true)
    setIsStale(true)
  }

  const insertText = (before: string, after: string = "") => {
    const textarea = textareaRef.current
    if (!textarea) return

    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = content.substring(start, end)
    const newText = content.substring(0, start) + before + selectedText + after + content.substring(end)

    handleContentChange(newText)

    // Set cursor position after insertion
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length)
    }, 0)
  }

  const formatButtons = [
    { icon: Bold, label: "Bold", action: () => insertText("**", "**") },
    { icon: Italic, label: "Italic", action: () => insertText("*", "*") },
    { icon: List, label: "List", action: () => insertText("- ") },
    { icon: Quote, label: "Quote", action: () => insertText("> ") },
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>{t("knowledgeBase.loading")}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Editor - Takes up 2/3 of the space */}
          <div className="lg:col-span-2">
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                    <FileText className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {t("knowledgeBase.title")}
                    </CardTitle>
                    <p className="text-gray-600 mt-1">{t("knowledgeBase.description")}</p>
                  </div>
                </div>

                {existingEntry && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-700">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      {t("knowledgeBase.editing_info", {
                        date: new Date(existingEntry.updatedAt).toLocaleDateString()
                      })}
                    </div>
                  </div>
                )}

                {hasUnsavedChanges && (
                  <div className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg text-sm text-amber-700">
                    <div className="flex items-center gap-2">
                      <Save className="h-4 w-4" />
                      {t("knowledgeBase.unsaved_changes")}
                    </div>
                  </div>
                )}
              </CardHeader>

              <CardContent className="space-y-4">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-4">
                    <Label htmlFor="knowledge-content" className="text-lg font-semibold flex items-center gap-2">
                      <Type className="h-5 w-5" />
                      {t("knowledgeBase.label")}
                    </Label>

                    {/* Formatting Toolbar */}
                    <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
                      <span className="text-sm font-medium text-gray-600">Format:</span>
                      {formatButtons.map((button, index) => (
                        <Button
                          key={index}
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={button.action}
                          className="h-8 w-8 p-0 hover:bg-gray-200"
                          title={button.label}
                        >
                          <button.icon className="h-4 w-4" />
                        </Button>
                      ))}
                      <Separator orientation="vertical" className="h-6 mx-2" />
                      <span className="text-xs text-gray-500">Markdown supported</span>
                    </div>

                    {/* Enhanced Textarea */}
                    <div className="relative">
                      <Textarea
                        ref={textareaRef}
                        id="knowledge-content"
                        value={content}
                        onChange={(e) => handleContentChange(e.target.value)}
                        className="min-h-[600px] resize-none text-base leading-relaxed p-6 border-2 border-gray-200 focus:border-blue-500 transition-colors duration-200 bg-white/50 backdrop-blur-sm"
                        placeholder={t("knowledgeBase.placeholder")}
                        disabled={saving}
                      />
                      <div className="absolute bottom-4 right-4 text-xs text-gray-400 bg-white/80 px-2 py-1 rounded">
                        {content.length} characters
                      </div>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-12 text-lg font-semibold bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg"
                    disabled={saving || !content.trim()}
                  >
                    {saving ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        {existingEntry ? t("knowledgeBase.updating") : t("knowledgeBase.saving")}
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-5 w-5" />
                        {existingEntry ? t("knowledgeBase.update_button") : t("knowledgeBase.save_button")}
                      </>
                    )}
                  </Button>

                  {message && (
                    <p
                      className={`text-sm pt-2 ${message.includes("success") || message.includes("updated")
                        ? "text-green-600"
                        : "text-red-600"
                        }`}
                    >
                      {message}
                    </p>
                  )}
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Right Sidebar - Analytics */}
          <div className="lg:col-span-1">
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  Analytics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {analytics && (
                  <>
                    <div className="space-y-4">
                      <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-600">Total Entries</span>
                          <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                            {analytics.totalEntries}
                          </Badge>
                        </div>
                      </div>

                      <div className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-600">Total Characters</span>
                          <Badge variant="secondary" className="bg-green-100 text-green-700">
                            {analytics.totalCharacters.toLocaleString()}
                          </Badge>
                        </div>
                      </div>

                      <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-600">Avg. Entry Length</span>
                          <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                            {Math.round(analytics.averageLength)}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {analytics.totalEntries > 0 && (
                      <div className="space-y-3">
                        <h4 className="font-semibold text-gray-700">Progress</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Knowledge Base Completion</span>
                            <span>{Math.min(100, Math.round((analytics.totalCharacters / 10000) * 100))}%</span>
                          </div>
                          <Progress
                            value={Math.min(100, (analytics.totalCharacters / 10000) * 100)}
                            className="h-2"
                          />
                          <p className="text-xs text-gray-500">
                            Target: 10,000 characters for comprehensive knowledge base
                          </p>
                        </div>
                      </div>
                    )}
                  </>
                )}

                {!analytics && (
                  <div className="text-center py-8">
                    <BarChart3 className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 text-sm">Analytics will appear after saving content</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Tips Card */}
            <Card className="mt-6 shadow-xl border-0 bg-gradient-to-br from-amber-50 to-orange-50">
              <CardHeader>
                <CardTitle className="text-lg text-amber-800">💡 Writing Tips</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-amber-700">
                  <li>• Use **bold** and *italic* for emphasis</li>
                  <li>• Create lists with - or numbered items</li>
                  <li>• Use > for important quotes</li>
                  <li>• Break content into clear sections</li>
                  <li>• Include specific examples and details</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
      ? "text-green-600"
      : "text-red-600"
                        } `}
                    >
                      {message}
                    </p>
                  )}
                </form>
              </div>

              {/* Right - Sidebar Analytics */}
              <div hidden className="w-full lg:w-80 p-4 bg-gray-50 rounded-md border space-y-6 relative">
                {isStale && (
                  <div className="absolute top-0 left-0 w-full bg-yellow-100 text-yellow-800 text-sm p-2 border-b border-yellow-300 rounded-t-md">
                    {t("knowledgeBase.stale_warning")}
                  </div>
                )}

                <div className={isStale ? "pt-8" : ""}>
                  <h2 className="font-semibold text-lg">
                    {t("knowledgeBase.kb_strength")}
                  </h2>
                  <p className="text-sm text-gray-600 mb-2">
                    {t("knowledgeBase.kb_strength_desc")}
                  </p>
                  <Progress value={kbStrength} className="h-3" />
                  <p className="text-sm text-gray-700 mt-1">
                    {t("knowledgeBase.entries_count", { strength: kbStrength, count: totalEntries })}
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold">{t("knowledgeBase.keywords")}</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {keywords.map((word, idx) => (
                      <Badge key={idx} variant="secondary">
                        {word}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold">{t("knowledgeBase.inferred_rules")}</h3>
                  <ul className="list-disc pl-5 mt-2 text-sm text-gray-700 space-y-1">
                    {rules.map((rule, idx) => (
                      <li key={idx}>{rule}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold">
                    {t("knowledgeBase.suggested_templates")}
                  </h3>
                  <ul className="list-disc pl-5 mt-2 text-sm text-gray-700 space-y-1">
                    {templates.map((template, idx) => (
                      <li key={idx}>{template}</li>
                    ))}
                  </ul>
                </div>
              </div>
          </div>
          )
}
