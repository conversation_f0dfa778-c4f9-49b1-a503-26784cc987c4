/* Custom styles for BlockNote editor */
.blocknote-editor {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  padding: 24px;
}

.blocknote-editor .bn-editor {
  min-height: 550px;
  padding: 0;
}

.blocknote-editor .bn-block-content {
  padding: 8px 0;
}

.blocknote-editor .bn-block-content p {
  margin: 0;
  padding: 4px 0;
}

.blocknote-editor .bn-block-content h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 16px 0 8px 0;
  color: #1f2937;
}

.blocknote-editor .bn-block-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 14px 0 6px 0;
  color: #374151;
}

.blocknote-editor .bn-block-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 12px 0 4px 0;
  color: #4b5563;
}

.blocknote-editor .bn-block-content ul,
.blocknote-editor .bn-block-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.blocknote-editor .bn-block-content li {
  margin: 4px 0;
}

.blocknote-editor .bn-block-content blockquote {
  border-left: 4px solid #3b82f6;
  background: #f8fafc;
  margin: 12px 0;
  padding: 12px 16px;
  font-style: italic;
  color: #475569;
}

.blocknote-editor .bn-block-content code {
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: #e11d48;
}

.blocknote-editor .bn-block-content pre {
  background: #1e293b;
  color: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  margin: 12px 0;
  overflow-x: auto;
}

.blocknote-editor .bn-block-content pre code {
  background: transparent;
  color: inherit;
  padding: 0;
}

/* Toolbar styling */
.bn-toolbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.bn-toolbar-button {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.bn-toolbar-button:hover {
  background: #f3f4f6;
  transform: translateY(-1px);
}

.bn-toolbar-button[data-active="true"] {
  background: #3b82f6;
  color: white;
}

/* Side menu styling */
.bn-side-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Drag handle styling */
.bn-drag-handle {
  color: #9ca3af;
  transition: color 0.2s ease;
}

.bn-drag-handle:hover {
  color: #3b82f6;
}

/* Focus styles */
.blocknote-editor .bn-editor:focus-within {
  outline: none;
}

/* Placeholder styling */
.bn-block-content[data-placeholder]:before {
  color: #9ca3af;
  font-style: italic;
}

/* Selection styling */
.blocknote-editor ::selection {
  background: rgba(59, 130, 246, 0.2);
}

/* Custom scrollbar */
.blocknote-editor .bn-editor::-webkit-scrollbar {
  width: 8px;
}

.blocknote-editor .bn-editor::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.blocknote-editor .bn-editor::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.blocknote-editor .bn-editor::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
