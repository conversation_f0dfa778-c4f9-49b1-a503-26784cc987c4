/* Custom styles for Tiptap editor */
.tiptap-editor {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.tiptap-editor .ProseMirror {
  outline: none;
  font-weight: 500; /* Medium font weight */
  font-size: 16px;
  line-height: 1.7;
  color: #374151;
}

.tiptap-editor .ProseMirror p {
  margin: 0.75rem 0;
}

.tiptap-editor .ProseMirror p:first-child {
  margin-top: 0;
}

.tiptap-editor .ProseMirror p:last-child {
  margin-bottom: 0;
}

.tiptap-editor .ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  color: #1f2937;
  line-height: 1.2;
}

.tiptap-editor .ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.75rem 0 0.75rem 0;
  color: #374151;
  line-height: 1.3;
}

.tiptap-editor .ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.5rem 0 0.5rem 0;
  color: #4b5563;
  line-height: 1.4;
}

.tiptap-editor .ProseMirror h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 1.25rem 0 0.5rem 0;
  color: #6b7280;
  line-height: 1.4;
}

.tiptap-editor .ProseMirror ul,
.tiptap-editor .ProseMirror ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.tiptap-editor .ProseMirror li {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.tiptap-editor .ProseMirror blockquote {
  border-left: 4px solid #3b82f6;
  background: linear-gradient(to right, #f8fafc, #ffffff);
  margin: 1.5rem 0;
  padding: 1rem 1.5rem;
  font-style: italic;
  color: #475569;
  border-radius: 0 8px 8px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tiptap-editor .ProseMirror code {
  background: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #e11d48;
  font-weight: 500;
}

.tiptap-editor .ProseMirror pre {
  background: #1e293b;
  color: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1.5rem 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
}

.tiptap-editor .ProseMirror pre code {
  background: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

.tiptap-editor .ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
  text-underline-offset: 2px;
  transition: color 0.2s ease;
}

.tiptap-editor .ProseMirror a:hover {
  color: #1d4ed8;
}

.tiptap-editor .ProseMirror strong {
  font-weight: 700;
  color: #1f2937;
}

.tiptap-editor .ProseMirror em {
  font-style: italic;
  color: #4b5563;
}

/* Placeholder styling */
.tiptap-editor .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
  font-style: italic;
}

/* Selection styling */
.tiptap-editor .ProseMirror ::selection {
  background: rgba(59, 130, 246, 0.2);
}

/* Focus styles */
.tiptap-editor .ProseMirror:focus {
  outline: none;
}

/* Custom scrollbar */
.tiptap-editor .ProseMirror::-webkit-scrollbar {
  width: 8px;
}

.tiptap-editor .ProseMirror::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.tiptap-editor .ProseMirror::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.tiptap-editor .ProseMirror::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Bubble menu styling (if added later) */
.bubble-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  display: flex;
  gap: 0.25rem;
}

.bubble-menu button {
  padding: 0.5rem;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.bubble-menu button:hover {
  background: #f3f4f6;
  color: #374151;
}

.bubble-menu button.is-active {
  background: #3b82f6;
  color: white;
}
