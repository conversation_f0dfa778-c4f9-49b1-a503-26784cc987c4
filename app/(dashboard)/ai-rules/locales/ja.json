{"title": "AIルールを編集", "subtitle": "ルールの条件、アクション、メタデータを変更", "page_title": "AIル<PERSON>ル", "page_subtitle": "CS業務のためのAIルールを管理・監視", "headers": {"name": "名前", "description": "説明", "tags": "タグ", "is_active": "アクティブ", "created_date": "作成日", "updated_date": "更新日", "created_by": "作成者"}, "common": {"no": "いいえ"}, "sections": {"basicInfo": {"name": "基本情報", "description": "ルールの名前と概要"}, "ruleLogic": {"name": "ルールロジック", "description": "条件とアクションを定義"}, "metadata": {"name": "メタデータ", "description": "タグとステータス"}, "systemInfo": {"name": "システム情報", "description": "追跡情報"}}, "fields": {"name": {"label": "ルール名", "placeholder": "このルールの説明的な名前を入力してください", "description": "このルールの目的を識別する明確で説明的な名前", "examples": "例：カート放棄リマインダー、新規顧客歓迎、価格問い合わせ回答"}, "description": {"label": "説明", "placeholder": "このルールが何をするか、いつトリガーされるかを説明してください...", "description": "ルールの目的、動作、期待される結果の詳細な説明", "examples": "例：\n• このルールは顧客がカートを放棄したときにフォローアップメッセージを送信します\n• 新規顧客を自動的に歓迎し、サポートを提供します\n• 顧客が費用について質問したときに価格情報を提供します"}, "conditions": {"label": "条件", "placeholder": "トリガーフレーズをカンマで区切って入力してください", "description": "このルールをトリガーするキーワード、フレーズ、またはパターン。複数の条件を区切るにはカンマを使用してください。", "examples": "例：\n• こんにちは、おはよう、こんばんは、挨拶\n• 価格、費用、いくら、値段\n• ヘルプ、サポート、助けて、支援\n• 苦情、問題、トラブル、クレーム"}, "actions": {"label": "アクション", "placeholder": "AIが取るべきアクションをカンマで区切って入力してください", "description": "条件が満たされたときにAIが行うべきこと。応答動作や特定のアクションを説明してください。", "examples": "例：\n• 温かく挨拶し、サポートを提供し、どのように助けられるか尋ねる\n• 価格情報を提供し、現在のプロモーションについて言及する\n• 人間のエージェントにエスカレーションし、連絡先詳細を収集する\n• 心から謝罪し、解決策を提供し、フォローアップする"}, "tags": {"label": "タグ", "placeholder": "整理用のタグを追加（オプション）", "description": "ルールの分類と整理に役立つオプションのラベル", "examples": "例：カスタマーサービス、営業、サポート、マーケティング、自動化、緊急"}, "isActive": {"label": "アクティブ", "description": "このルールを有効または無効にする"}, "createdAt": {"label": "作成日"}, "updatedAt": {"label": "最終更新"}, "createdBy": {"label": "作成者"}}, "buttons": {"update": "ルールを更新", "cancel": "キャンセル"}, "validation": {"nameRequired": "ルール名は必須です", "nameMinLength": "ルール名は最低2文字必要です", "nameMaxLength": "ルール名は100文字を超えることはできません", "descriptionMaxLength": "説明は500文字を超えることはできません", "conditionsRequired": "少なくとも1つの条件が必要です", "actionsRequired": "少なくとも1つのアクションが必要です"}, "messages": {"updateSuccess": "AIルールが正常に更新されました", "updateError": "AIルールの更新に失敗しました", "fetchError": "AIルールの取得に失敗しました", "notFound": "AIルールが見つかりません"}}