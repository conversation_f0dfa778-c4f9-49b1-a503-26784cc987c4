import { ERROR_CODES } from "@/app/api/error_codes"
import { driver } from "@/lib/repositories/LiveRedisDriver"
import { SessionContext } from "@/lib/repositories/auth/types"
import { knowledgeBaseBusinessLogic } from "@/lib/repositories/businessLogics"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

const ParserCallbackSchema = z.object({
  knowledgeBaseId: z.string().min(1, "Knowledge base ID is required"),
  datasources: z.array(z.object({
    name: z
      .string()
      .min(1, "Name is required")
      .max(100, "Name must be less than 100 characters"),
    type: z.string().min(1, "Type is required"),
    url: z.string().url("Must be a valid URL").optional(),
    content: z.string().optional(),
  })).optional(),
  aiRules: z.array(z.object({
    name: z.string().min(1, "Name is required"),
    description: z.string().optional(),
    conditions: z
      .array(z.string().min(1, "Condition cannot be empty"))
      .min(1, "At least one condition is required"),
    actions: z
      .array(z.string().min(1, "Action cannot be empty"))
      .min(1, "At least one action is required"),
    tags: z.array(z.string()).optional(),
  })).optional(),
  messageTemplates: z.array(z.object({
    title: z.string().min(1, "Title is required"),
    query: z.string().min(1, "Query is required"),
    template: z.string().min(1, "Template is required"),
    category: z.string().optional(),
    tags: z.array(z.string()).optional(),
    variables: z.array(z.string()).optional(),
  })).optional(),
  knowledgeBases: z.array(z.object({
    title: z.string().min(1, "Title is required"),
    content: z.string().min(1, "Content is required"),
    keywords: z.array(z.string()).optional(),
    category: z.string().optional(),
    tags: z.array(z.string()).optional(),
  })).optional(),
})

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const callbackContext = req.nextUrl.searchParams.get("context") as "KNOWLEDGEBASES" | "AIRULES" | "DATASOURCES" | "MESSAGETEMPLATES"
    const parseId = req.nextUrl.searchParams.get("parseId") as string

    const validationResult = ParserCallbackSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 }
      )
    }

    const context = await driver.get<SessionContext>(`knowledge-base-parse-context-${body.knowledgeBaseId}`)
    if (!context) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Context not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 }
      )
    }

    const result = await knowledgeBaseBusinessLogic.handleParserCallback(
      parseId,
      callbackContext,
      validationResult.data as any,
      context,
    )

    return NextResponse.json(
      new ResponseWrapper("success", result),
      { status: 200 }
    )
  } catch (error: any) {
    console.error(
      "Error in POST /api/v1/knowledge-base/parser-callback:",
      error,
    )
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
