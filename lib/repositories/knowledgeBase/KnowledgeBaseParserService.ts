import { KnowledgeBaseParserService } from "./interface"
import { ERROR_CODES } from "@/app/api/error_codes"

const PARSER_ENGINE_URL = process.env.PARSER_ENGINE_URL!
const PARSER_CALLBACK_URL = process.env.PARSER_CALLBACK_URL!
const INTERNAL_SECRET_TOKEN = process.env.INTERNAL_SECRET_TOKEN!

const kWORDLIMIT = 100000

export class KnowledgebaseParserService implements KnowledgeBaseParserService {
    async parseKnowledgeBaseContent(
        parseId: string,
        knowledgeBaseId: string,
        content: string,
        context: "KNOWLEDGEBASES" | "AIRULES" | "DATASOURCES" | "MESSAGETEMPLATES" = "KNOWLEDGEBASES"
    ): Promise<{
        status: "success" | "failed"
        errors?: string[]
        errorCodes?: string[]
    }> {
        try {
            const callbackUrl = `${PARSER_CALLBACK_URL}?context=${context}&parseId=${parseId}`
            const chunks = this.splitContent(content)

            for (let i = 0; i < chunks.length; i++) {
                const payload = {
                    knowledgeBaseId,
                    content: chunks[i],
                    context,
                }

                const res = await fetch(PARSER_ENGINE_URL, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-Parser-Webhook-Endpoint": callbackUrl,
                        "X-Internal-System-Token": INTERNAL_SECRET_TOKEN,
                    },
                    body: JSON.stringify(payload),
                })

                if (!res.ok) {
                    const text = await res.text()
                    return {
                        status: "failed",
                        errors: [`Parser webhook error (chunk ${i + 1}): ${res.status} - ${text}`],
                        errorCodes: [ERROR_CODES.EXTERNAL_SERVICE_ERROR],
                    }
                }
            }

            return { status: "success" }

        } catch (error: any) {
            return {
                status: "failed",
                errors: [error.message || "Unknown error sending to parser webhook"],
                errorCodes: [ERROR_CODES.EXTERNAL_SERVICE_ERROR],
            }
        }
    }

    /**
     * Splits content by markdown H2 or fallback to word chunking
     */
    private splitContent(content: string, wordLimit = kWORDLIMIT, overlapRatio = 0.3): string[] {
        const sections = content.split(/^##\s+/gm).filter(s => s.trim() !== "")
        if (sections.length > 1) {
            return sections.map(s => `## ${s.trim()}`)
        }

        const words = content.split(/\s+/)
        const chunks: string[] = []

        const step = Math.floor(wordLimit * (1 - overlapRatio))
        for (let i = 0; i < words.length; i += step) {
            const chunkWords = words.slice(i, i + wordLimit)
            chunks.push(chunkWords.join(" "))
        }

        return chunks
    }
}
