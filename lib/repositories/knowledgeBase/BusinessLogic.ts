import {
  KnowledgeBaseEntry,
  KnowledgeBaseDocument,
  KnowledgeBaseStats,
  KnowledgeBaseCreateInput,
  KnowledgeBaseUpdateInput,
  KnowledgeBaseDocumentCreateInput,
  KnowledgeBaseQueryParams,
  KnowledgeBaseBusinessLogicInterface,
  KnowledgeBaseParserService,
  ParsedContentCallback,
  KnowledgeBaseParsedCreateInput,
  KnowledgeBaseParsedEntry,
} from "./interface"
import { SessionContext } from "../auth/types"
import { createError, generateId } from "@/lib/utils/common"
import {
  KnowledgeBaseDBRepository,
  KnowledgeBaseParsedDBRepository,
} from "./DBRepository"
import { KnowledgeBaseParsedVectorDBRepository } from "./KnowledgeBaseParsedVectorDBRepository"
import { DatasourceBusinessLogic } from "../datasources"
import { AiRuleBusinessLogic } from "../aiRules"
import { MessageTemplateBusinessLogic } from "../messageTemplates"
import { driver } from "../LiveRedisDriver"

export class KnowledgeBaseBusinessLogic
  implements KnowledgeBaseBusinessLogicInterface {

  constructor(
    private db: KnowledgeBaseDBRepository,
    private parsedDb: KnowledgeBaseParsedDBRepository,
    private parsedVectorDb: KnowledgeBaseParsedVectorDBRepository,
    private otherBusinessLogic: {
      dataSource: DatasourceBusinessLogic,
      aiRule: AiRuleBusinessLogic,
      messageTemplate: MessageTemplateBusinessLogic,
    },
    private parserService: KnowledgeBaseParserService,
  ) {
  }

  // Validation helpers
  private validateId(id: string): void {
    if (!id || typeof id !== "string" || id.trim() === "") {
      throw createError(
        "Knowledge base entry ID is required",
        "VALIDATION_FAILED",
      )
    }
  }

  private validateCreateInput(data: KnowledgeBaseCreateInput): void {
    if (!data.content || data.content.trim() === "") {
      throw createError("Content is required", "VALIDATION_FAILED")
    }
  }

  private buildContextFilters(context: SessionContext) {
    const filters = []

    // Add createdBy filter using user.id
    if (context.user.id) {
      filters.push({ field: "createdBy", value: context.user.id })
    }

    // Add organization filter if available
    if (context.organization?.id) {
      filters.push({ field: "organizationId", value: context.organization.id })
    }

    return filters
  }

  private validateDocumentCreateInput(
    data: KnowledgeBaseDocumentCreateInput,
  ): void {
    if (!data.name || data.name.trim() === "") {
      throw createError("Document name is required", "VALIDATION_FAILED")
    }
    if (!data.filePath || data.filePath.trim() === "") {
      throw createError("File path is required", "VALIDATION_FAILED")
    }
  }

  // Entry management
  async getById(
    id: string,
    context: SessionContext,
    includeDeleted = false,
  ): Promise<KnowledgeBaseEntry | null> {
    this.validateId(id)
    const entry = await this.db.getById(id, includeDeleted)

    // Filter by context - ensure user can only access their own organization's entries
    if (entry && context.organization && entry.organizationId !== context.organization.id) {
      return null
    }

    // Also check if user created this entry (for additional security)
    if (entry && context.user.id && entry.createdBy !== context.user.id && !context.organization) {
      return null
    }

    return entry
  }

  async getAll(
    params: KnowledgeBaseQueryParams,
    context: SessionContext,
  ): Promise<{ items: KnowledgeBaseEntry[]; total: number }> {
    // Use buildContextFilters for consistent filtering
    const contextFilters = this.buildContextFilters(context)
    const queryParams = {
      ...params,
      filters: [
        ...(params.filters || []),
        ...contextFilters,
      ],
    }

    return this.db.getAll(queryParams)
  }

  async create(
    data: KnowledgeBaseCreateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseEntry> {
    this.validateCreateInput(data)

    const parseId = generateId("knowledge-base-parse");
    const entryData: KnowledgeBaseCreateInput = {
      ...data,
      parseId: parseId
    }

    const entry = await this.db.create({
      ...entryData,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    })

    // FIRE-AND-FORGET: Send to external parser service
    await driver.set(`knowledge-base-parse-context-${entry.id}`, context)
    this.parserService.parseKnowledgeBaseContent(entry.id, entry.content, parseId).catch(error => {
      console.error("Failed to send to parser service:", error)
    })

    return entry
  }

  async update(
    id: string,
    data: KnowledgeBaseUpdateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseEntry | null> {
    this.validateId(id)

    // Check if entry exists and user has permission to update it
    const existingEntry = await this.getById(id, context)
    if (!existingEntry) {
      return null
    }
    const parseId = generateId("knowledge-base-parse");

    const updateData: KnowledgeBaseUpdateInput = {
      ...data,
      parseId: parseId
    }

    const entry = await this.db.update(id, updateData)

    if (entry && updateData.content) {
      // FIRE-AND-FORGET: Send to external parser service
      await driver.set(`knowledge-base-parse-context-${entry.id}`, context)
      this.parserService.parseKnowledgeBaseContent(entry.id, entry.content, parseId).catch(error => {
        console.error("Failed to send to parser service:", error)
      })
    }

    return entry
  }

  async delete(
    id: string,
    context: SessionContext,
    hardDelete = false,
  ): Promise<boolean> {
    this.validateId(id)

    // Check if entry exists and user has permission to delete it
    const existingEntry = await this.getById(id, context)
    if (!existingEntry) {
      return false
    }

    // If hard delete, also delete related content
    if (hardDelete) {
      try {
        await this.deleteRelatedContent(id, context)
      } catch (error) {
        console.error("Failed to delete related content:", error)
        // Continue with deletion even if related content deletion fails
      }
    }

    const success = await this.db.delete(id, hardDelete)

    return success
  }

  async restore(id: string, context: SessionContext): Promise<boolean> {
    this.validateId(id)

    // Check if entry exists and user has permission to restore it
    const existingEntry = await this.getById(id, context, true) // includeDeleted = true
    if (!existingEntry) {
      return false
    }

    return this.db.restore(id)
  }

  async getStats(context: SessionContext): Promise<KnowledgeBaseStats> {
    const [entriesResult] = await Promise.all([
      this.getAll({ page: 1, limit: 1 }, context),
    ])

    // Calculate a simple score based on content availability
    const totalEntries = entriesResult.total
    const totalDocuments = 0
    const score = Math.min(100, (totalEntries + totalDocuments) * 5)

    return {
      score,
      totalEntries,
      totalDocuments,
      keywords: [], // Would be extracted from all entries
      inferredRules: [], // Would be generated by AI
      suggestedTemplates: [], // Would be generated by AI
      lastUpdated: new Date(),
    }
  }

  async generateInferredRules(context: SessionContext): Promise<string[]> {
    // This would use AI to analyze knowledge base and generate rules
    // For now, return empty array
    return []
  }

  async generateSuggestedTemplates(context: SessionContext): Promise<string[]> {
    // This would use AI to analyze knowledge base and generate templates
    // For now, return empty array
    return []
  }

  async extractKeywords(
    content: string,
    context: SessionContext,
  ): Promise<string[]> {
    // Simple keyword extraction - in production, use AI/NLP
    const words = content
      .toLowerCase()
      .replace(/[^\w\s]/g, " ")
      .split(/\s+/)
      .filter((word) => word.length > 3)
      .filter((word, index, arr) => arr.indexOf(word) === index)
      .slice(0, 10)

    return words
  }

  async searchSimilar(
    query: string,
    context: SessionContext,
    limit = 5,
  ): Promise<KnowledgeBaseParsedEntry[]> {
    if (!query || !query.trim()) {
      return []
    }

    const searchTerm = query.trim()
    const vectorResults = await this.parsedVectorDb.searchSimilar(searchTerm, limit, context)

    const filteredResults = []

    for (const vectorResult of vectorResults) {
      const entryResult = await this.parsedDb.getById(vectorResult.id, false)
      if (entryResult?.createdBy === context.user.id) {
        filteredResults.push(entryResult)
      }
    }

    return filteredResults
  }

  async createParsed(
    data: KnowledgeBaseParsedCreateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseParsedEntry> {

    const entry = await this.parsedDb.create({
      ...data,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    })

    await this.parsedVectorDb.create(entry, context)

    return entry
  }

  async handleParserCallback(
    parseId: string,
    callbackContext: "KNOWLEDGEBASES" | "AIRULES" | "DATASOURCES" | "MESSAGETEMPLATES",
    callbackData: ParsedContentCallback,
    context: SessionContext,
  ): Promise<{
    createdIds: string[]
  }> {
    // Get the knowledge base entry
    const knowledgeBaseEntry = await this.getById(callbackData.knowledgeBaseId, context)
    if (!knowledgeBaseEntry) {
      throw createError("Knowledge base entry not found", "NOT_FOUND")
    }

    const createdIds: string[] = []

    try {
      // Handle different contexts
      switch (callbackContext) {
        case "DATASOURCES":
          if (callbackData.datasources) {
            for (const chunk of callbackData.datasources) {
              try {
                const datasource = await this.otherBusinessLogic.dataSource.create(
                  {
                    ...chunk,
                    isActive: true,
                    sourceKnowledgeBaseId: callbackData.knowledgeBaseId,
                  },
                  context
                )
                createdIds.push(datasource.id)
              } catch (error) {
                console.log("Failed to create datasource from chunk:", error)
              }
            }
          }
          break

        case "AIRULES":
          if (callbackData.aiRules) {
            for (const chunk of callbackData.aiRules) {
              try {
                const aiRule = await this.otherBusinessLogic.aiRule.create(
                  {
                    name: chunk.name,
                    description: chunk.description,
                    conditions: chunk.conditions,
                    actions: chunk.actions,
                    tags: chunk.tags,
                    isActive: true,
                    sourceKnowledgeBaseId: callbackData.knowledgeBaseId,
                  },
                  context
                )
                createdIds.push(aiRule.id)
              } catch (error) {
                console.log("Failed to create AI rule from chunk:", error)
              }
            }
          }
          break

        case "MESSAGETEMPLATES":
          if (callbackData.messageTemplates) {
            for (const chunk of callbackData.messageTemplates) {
              try {
                const messageTemplate = await this.otherBusinessLogic.messageTemplate.create(
                  {
                    title: chunk.title,
                    query: chunk.query,
                    template: chunk.template,
                    tags: chunk.tags,
                    isActive: true,
                    createdBy: context.user.id,
                    organizationId: context.organization?.id,
                    sourceKnowledgeBaseId: callbackData.knowledgeBaseId,
                  },
                  context
                )
                createdIds.push(messageTemplate.id)
              } catch (error) {
                console.log("Failed to create message template from chunk:", error)
              }
            }
          }
          break

        case "KNOWLEDGEBASES":
          if (knowledgeBaseEntry.parseId !== parseId) {
            await this.deleteRelatedContent(callbackData.knowledgeBaseId, context)
          }
          if (callbackData.knowledgeBases) {
            for (const chunk of callbackData.knowledgeBases) {
              try {
                const knowledgeBaseParsed = await this.createParsed(
                  {
                    ...chunk,
                    sourceKnowledgeBaseId: callbackData.knowledgeBaseId,
                  },
                  context
                )
                createdIds.push(knowledgeBaseParsed.id)
              } catch (error) {
                console.log("Failed to create knowledge base from chunk:", error)
              }
            }
            const knowledgeBase = await this.update(
              callbackData.knowledgeBaseId,
              {
                relatedParsed: createdIds,
              },
              context
            )
          }
          break
      }
      return {
        createdIds: createdIds,
      }
    } catch (error) {
      console.log("Error in handleParserCallback:", error)
      throw createError(
        "Failed to process parser callback",
        "PROCESSING_FAILED"
      )
    }
  }

  async deleteParsed(id: string, context: SessionContext, hardDelete = false): Promise<boolean> {
    await this.parsedDb.delete(id, hardDelete)
    await this.parsedVectorDb.delete(id, context)
    return true
  }

  async deleteRelatedContent(
    knowledgeBaseId: string,
    context: SessionContext,
  ): Promise<boolean> {
    try {
      const knowledgeBaseEntry = await this.getById(knowledgeBaseId, context)
      if (!knowledgeBaseEntry) {
        return true
      }

      if (knowledgeBaseEntry.relatedParsed) {
        for (const id of knowledgeBaseEntry.relatedParsed) {
          try {
            await this.deleteParsed(
              id,
              context,
              true
            )
          } catch (error) {
            console.error(`Failed to delete parsed ${id}:`, error)
            // Continue with other deletions even if one fails
          }
        }
      }

      return true
    } catch (error) {
      console.error("Error in deleteRelatedContent:", error)
      throw createError(
        "Failed to delete related content",
        "DELETION_FAILED"
      )
    }
  }
}
