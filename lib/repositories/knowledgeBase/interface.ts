import { AiRuleCreateInput } from "../aiRules"
import { SessionContext } from "../auth/types"
import { DatasourceCreateInput } from "../datasources"
import { MessageTemplateCreateInput } from "../messageTemplates"

export interface KnowledgeBaseEntry {
  id: string
  content: string
  keywords: string[]
  category?: string
  tags?: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  organizationId?: string
  relatedParsed?: string[]
  parseId: string;
}

export interface KnowledgeBaseParsedEntry {
  id: string
  title: string
  content: string
  keywords: string[]
  category?: string
  tags?: string[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  organizationId?: string
  sourceKnowledgeBaseId?: string
}

export interface KnowledgeBaseDocument {
  id: string
  name: string
  originalName: string
  size: number
  mimeType: string
  filePath: string
  extractedContent?: string
  keywords: string[]
  isProcessed: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
}

export interface KnowledgeBaseStats {
  score: number // 0-100
  totalEntries: number
  totalDocuments: number
  keywords: string[]
  inferredRules: string[]
  suggestedTemplates: string[]
  lastUpdated: Date
}

export interface KnowledgeBaseCreateInput {
  content: string
  parseId: string;
}

export interface KnowledgeBaseParsedCreateInput {
  title: string
  content: string
  keywords: string[]
  category?: string
  tags?: string[]
  sourceKnowledgeBaseId?: string
}

export interface KnowledgeBaseUpdateInput {
  content?: string
  relatedParsed?: string[]
  parseId?: string;
}

export interface KnowledgeBaseDocumentCreateInput {
  name: string
  originalName: string
  size: number
  mimeType: string
  filePath: string
  extractedContent?: string
  keywords?: string[]
  isProcessed?: boolean
  isActive?: boolean
}

export interface KnowledgeBaseDocumentUpdateInput {
  name?: string
  extractedContent?: string
  keywords?: string[]
  isProcessed?: boolean
  isActive?: boolean
  updatedBy?: string
  organizationId?: string
}

export interface KnowledgeBaseQueryParams {
  search?: string
  filters?: { field: keyof KnowledgeBaseEntry | string; value: any }[]
  sort?: {
    field: keyof KnowledgeBaseEntry | string
    direction: "ASC" | "DESC"
  }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface KnowledgeBaseDocumentQueryParams {
  search?: string
  filters?: { field: keyof KnowledgeBaseDocument | string; value: any }[]
  sort?: {
    field: keyof KnowledgeBaseDocument | string
    direction: "ASC" | "DESC"
  }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface KnowledgeBaseBusinessLogicInterface {
  // Entry management
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<KnowledgeBaseEntry | null>
  getAll(
    params: KnowledgeBaseQueryParams,
    context: SessionContext,
  ): Promise<{
    items: KnowledgeBaseEntry[]
    total: number
  }>
  create(
    data: KnowledgeBaseCreateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseEntry>
  update(
    id: string,
    data: KnowledgeBaseUpdateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseEntry | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>

  // Parsed
  createParsed(
    data: KnowledgeBaseParsedCreateInput,
    context: SessionContext,
  ): Promise<KnowledgeBaseParsedEntry>

  getStats(context: SessionContext): Promise<KnowledgeBaseStats>
  generateInferredRules(context: SessionContext): Promise<string[]>
  generateSuggestedTemplates(context: SessionContext): Promise<string[]>
  extractKeywords(content: string, context: SessionContext): Promise<string[]>

  // Search and AI operations
  searchSimilar(
    query: string,
    context: SessionContext,
    limit?: number,
  ): Promise<KnowledgeBaseParsedEntry[]>

  handleParserCallback(
    parseId: string,
    callbackContext: "KNOWLEDGEBASES" | "AIRULES" | "DATASOURCES" | "MESSAGETEMPLATES",
    callbackData: ParsedContentCallback,
    context: SessionContext,
  ): Promise<{
    createdIds: string[]
  }>
  deleteRelatedContent(
    knowledgeBaseId: string,
    context: SessionContext,
  ): Promise<boolean>
}

export interface ChunkResult {
  content: string;
  keywords: string[];
  title: string;
}

export interface KnowledgeBaseParserService {
  parseKnowledgeBaseContent(
    knowledgeBaseId: string,
    content: string,
    parseId: string,
    context?: "KNOWLEDGEBASES" | "AIRULES" | "DATASOURCES" | "MESSAGETEMPLATES"
  ): Promise<{
    status: "success" | "failed"
    errors?: string[]
    errorCodes?: string[]
  }>
}

// Callback data structure from external parser service
export interface ParsedContentCallback {
  knowledgeBaseId: string
  datasources?: DatasourceCreateInput[]
  aiRules?: AiRuleCreateInput[]
  messageTemplates?: MessageTemplateCreateInput[]
  knowledgeBases?: KnowledgeBaseParsedCreateInput[]
}